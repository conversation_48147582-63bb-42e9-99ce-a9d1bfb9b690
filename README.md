# PyxelEditToPng

![PyxelEditToPng logo](https://github.com/bolon667/PyxelEditToPng/blob/main/gitImages/PyxelEditToPng_logo.jpg)

Python script, which allows you to convert **.pyxel** files into **.png**.


## How to install

1. Download [Python](https://www.python.org/downloads/)
2. In script folder, run this command (from powershell or cmd) `pip install -r requirements.txt`

## How to use

1. Put .pyxel file(s) on script
2. Wait for generated png's



